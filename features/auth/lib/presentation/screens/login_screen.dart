import 'package:auth/core/constants/strings.dart';
import 'package:auth/presentation/stores/auth_store.dart';
import 'package:auth/presentation/widgets/auth_success_dialog.dart';
import 'package:core/core.dart' as core;
import 'package:di/di.dart';
import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:go_router/go_router.dart';
import 'package:logger/logger.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  late final AuthStore _authStore;
  late final Logger _logger;

  @override
  void initState() {
    super.initState();
    _authStore = di.get<AuthStore>();
    _logger = di.get<Logger>();
    _logger.i('LoginScreen initialized');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Observer(
        builder: (context) {
          return SignInScreen(
            providers: [EmailAuthProvider()],
            actions: [
              ForgotPasswordAction((context, email) {
                context.go(core.Routes.forgotPassword);
              }),
              AuthStateChangeAction<SignedIn>((context, state) async {
                _logger.i('User signed in successfully: ${state.user?.email}');

                if (mounted) {
                  _showSuccessDialog();
                  if (mounted) {
                    context.go(core.Routes.home);
                  }
                }
              }),
              AuthStateChangeAction<UserCreated>((context, state) {
                _logger.i('User created, navigating to email verification');
                context.go(core.Routes.emailVerification);
              }),
              AuthStateChangeAction<CredentialLinked>((context, state) {
                _logger.i('Credential linked successfully');
              }),
            ],
          );
        },
      ),
    );
  }

  Future<void> _showSuccessDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AuthSuccessDialog(
        title: 'Welcome Back!',
        message: AuthStrings.signInSuccess,
        onPressed: () => Navigator.of(context).pop(),
      ),
    );
  }
}
