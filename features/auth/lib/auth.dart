import 'package:auth/data/repositories/auth_repository.dart';
import 'package:auth/data/repositories/firebase_auth_repository.dart';
import 'package:auth/domain/services/auth_service.dart';
import 'package:auth/presentation/stores/auth_store.dart';
import 'package:di/di.dart';
import 'package:logger/logger.dart';

export 'core/index.dart';
export 'data/repositories/firebase_auth_repository.dart';
export 'domain/models/auth_exception.dart';
export 'domain/models/auth_state.dart';
export 'domain/models/auth_user.dart';
export 'domain/services/auth_service.dart';
export 'presentation/screens/login_screen.dart';
export 'presentation/stores/auth_store.dart';

Future<void> init() async {
  di.register<AuthRepository>(
    FirebaseAuthRepository(logger: di.get<Logger>()),
    dispose: (repo) => Future.value(),
  );
  di.register<AuthService>(
    AuthService(
      authRepository: di.get<AuthRepository>(),
      logger: di.get<Logger>(),
    ),
    dispose: (service) => Future.value(),
  );
  di.register<AuthStore>(
    AuthStore(
      authRepository: di.get<AuthRepository>(),
      logger: di.get<Logger>(),
    ),
    dispose: (store) async => store.dispose(),
  );
}

Future<void> dispose() async {
  if (di.has<AuthStore>()) {
    await di.unregister<AuthStore>(di.get<AuthStore>());
  }
  if (di.has<AuthService>()) {
    await di.unregister<AuthService>(di.get<AuthService>());
  }
  if (di.has<AuthRepository>()) {
    await di.unregister<AuthRepository>(di.get<AuthRepository>());
  }
  if (di.has<Logger>()) {
    await di.unregister<Logger>(di.get<Logger>());
  }
}
