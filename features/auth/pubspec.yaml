name: auth
description: "Firebase Authentication package for BiomeDict with MobX state management and go_router integration."
version: 0.1.0
publish_to: 'none'

environment:
  sdk: ^3.3.0
  flutter: ">=3.19.0"

dependencies:
  flutter:
    sdk: flutter

  # Firebase
  firebase_auth: ^5.6.0
  firebase_ui_auth: ^1.17.0
  firebase_core: ^3.14.0

  # State Management
  mobx: ^2.5.0
  flutter_mobx: ^2.3.0

  # Navigation
  go_router: ^15.2.0

  # Dependency Injection
  get_it: ^8.0.3

  # Logging
  logger: ^2.5.0

  # JSON Serialization
  json_annotation: ^4.9.0

  # Equality
  equatable: ^2.0.7

  # Local packages
  di:
    path: ../../packages/di
  core:
    path: ../core

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.5.1
  json_serializable: ^6.9.5
  mobx_codegen: ^2.7.1

flutter:

