import 'package:auth/auth.dart' as auth;
import 'package:biomedict/firebase_options.dart';
import 'package:biomedict/router/app_router.dart';
import 'package:core/core.dart' as core;
import 'package:design_system/design_system.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await core.init();
  await auth.init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    final brightness = View.of(context).platformDispatcher.platformBrightness;
    final materialTheme = DesignSystem.buildTheme(context);

    return MaterialApp.router(
      title: core.Strings.appName,
      theme: brightness == Brightness.light
          ? materialTheme.light()
          : materialTheme.dark(),
      routerConfig: AppRouter.createRouter(),
    );
  }
}
